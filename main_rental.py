import os, sys, logging
from colorlog import ColoredFormatter
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from datetime import datetime
from scraper.rental_scraper import is_allowed_to_scrape, scrape_rental_offer, get_rental_search_url, log_rental_scraping_summary
from scraper.rental_fetch_and_parse import download_rental_data_from_search_results, check_if_rental_offer_exists, check_if_rental_price_changed, find_closed_rental_offers
from db.db_setup import create_tables
from db.rental_db_operations import insert_new_rental_listing, update_active_rental_offers, update_deleted_rental_offers
from db.db_setup import get_db_connection

from config.logging_config import setup_logger
logger = setup_logger()

# ZASADY: WYSZUKIWANIE MIESZKAN NA WYNAJEM W DANYM MIESCIE BEZ ZADNYCH FILTROW, ZALECANE SORTOWANIE OD NAJNOWSZYCH I MAX LIMIT OFERT NA STRONE

# Przykładowe URL dla wynajmu mieszkań w różnych miastach
# url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/warszawa/warszawa/warszawa?viewType=listing&by=LATEST&direction=DESC&limit=72"
# url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/slaskie/katowice/katowice/katowice?viewType=listing&by=LATEST&direction=DESC&limit=72"

# Konfiguracja dla wynajmu
city = 'warszawa'
voivodeship = 'mazowieckie'
url = get_rental_search_url(city, voivodeship)

def main():
    conn = None
    cur = None
    
    # Liczniki dla podsumowania
    total_offers_processed = 0
    new_offers_added = 0
    updated_offers_count = 0
    closed_offers_count = 0
    
    try:
        conn = get_db_connection()
        if conn is None:
            logging.critical("Connection to the database failed")
            return
        cur = conn.cursor()
        
        # Upewnij się, że to dozwolone
        result = is_allowed_to_scrape(url)
        logging.warning(f"Is fetching rental page {url} allowed?: {result}")

        # Utwórz tabele jeżeli nie istnieją
        create_tables(cur)

        # Pobierz dane o ofertach wynajmu
        logging.info("Rozpoczynam pobieranie podstawowych danych z wyniku wyszukiwania ofert wynajmu...")
        all_rental_offers_basic = download_rental_data_from_search_results(url)
        
        # Ograniczenie dla testów
        all_rental_offers_basic = all_rental_offers_basic[:5]
        logging.info(f"Temporarily limiting processing to the first {len(all_rental_offers_basic)} rental offers for testing.")

        logging.debug(f"Dane z all_rental_offers_basic: \n{'--' * 100}\n{all_rental_offers_basic}\n {'--' * 100}\n")

        total_offers_processed = len(all_rental_offers_basic)

        # Sprawdź które oferty już są w bazie (i dodaj/aktualizuj cenę):
        logging.info(f"\nRozpoczynam sprawdzanie i pobieranie ofert wynajmu... (łącznie {total_offers_processed} ofert)")
        logging.info("UWAGA: Każda oferta będzie zapisywana do bazy NATYCHMIAST po przetworzeniu!")

        for i, offer in enumerate(all_rental_offers_basic, 1):
            id = offer.get("listing_id")
            if len(str(id)) != 8:
                logging.debug(f"Pomijam ofertę {id} - nieprawidłowy format ID")
                continue

            logging.info(f"[{i}/{total_offers_processed}] Przetwarzam ofertę wynajmu {id}...")

            # Jeżeli dana oferta nie znajduje się jeszcze w bazie, pobierz ją i zapisz
            try:
                if not check_if_rental_offer_exists(offer, cur):
                    logging.info(f"[{i}/{total_offers_processed}] Oferta {id} nie istnieje w bazie - pobieranie pełnych danych...")
                    offer_data = scrape_rental_offer(offer)  # pobierz znalezioną ofertę w całości

                    if offer_data:  # jeżeli brak błędu wstaw dane do bazy
                        logging.info(f"[{i}/{total_offers_processed}] Zapisuję ofertę {id} do bazy danych...")
                        id_db = insert_new_rental_listing(offer_data, conn, cur)  # wstaw do bazy (z automatycznym commit)

                        if id_db:
                            new_offers_added += 1
                            logging.info(f"✅ [{i}/{total_offers_processed}] Oferta wynajmu {id} ZAPISANA w bazie pod id {id_db}")
                            logging.info(f"   Tytuł: {offer_data.get('title', 'N/A')[:50]}...")
                            logging.info(f"   Cena: {offer_data.get('rental_price', 'N/A')} PLN")
                            logging.info(f"   Lokalizacja: {offer_data.get('city', 'N/A')}, {offer_data.get('street', 'N/A')}")
                        else:
                            logging.warning(f"❌ [{i}/{total_offers_processed}] Nie udało się zapisać oferty wynajmu {id} w bazie.")
                    else:
                        logging.warning(f"❌ [{i}/{total_offers_processed}] Nie udało się pobrać pełnych danych oferty wynajmu {id} – pomijam.")

                else:
                    # Oferta istnieje - sprawdź zmiany ceny
                    logging.info(f"[{i}/{total_offers_processed}] Oferta wynajmu {id} istnieje w bazie, sprawdzanie zmian ceny...")
                    price_check_result = check_if_rental_price_changed(offer, cur)

                    if price_check_result is not None:
                        id_db, new_price, new_price_per_m = price_check_result
                        # Jeżeli new_price to nie False tylko liczba tzn że cena się zmieniła - update bazy
                        if new_price is not False and new_price is not None:  # Indicates a price change was found
                            logging.info(f"[{i}/{total_offers_processed}] Aktualizuję cenę oferty {id} w bazie...")
                            update_active_rental_offers((id_db, new_price, new_price_per_m), conn, cur)  # z automatycznym commit
                            updated_offers_count += 1
                            logging.info(f"✅ [{i}/{total_offers_processed}] Cena oferty wynajmu {id} ({id_db}) ZAKTUALIZOWANA w bazie")
                            logging.info(f"   Nowa cena: {new_price} PLN, nowa cena za m2: {new_price_per_m} PLN/m2")
                        elif new_price is False:
                            logging.info(f"ℹ️  [{i}/{total_offers_processed}] Cena oferty wynajmu {id} ({id_db}) nie zmieniła się.")
                        else:  # new_price is None, but price_check_result was not None
                            logging.warning(f"⚠️  [{i}/{total_offers_processed}] check_if_rental_price_changed zwrócił nieoczekiwany wynik dla oferty wynajmu {id} ({id_db}): {price_check_result}")
                    else:
                        logging.error(f"❌ [{i}/{total_offers_processed}] Nie udało się sprawdzić zmiany ceny dla oferty wynajmu {id} (check_if_rental_price_changed zwrócił None).")

                # Potwierdzenie, że oferta została przetworzona
                logging.info(f"[{i}/{total_offers_processed}] Oferta {id} przetworzona pomyślnie.\n")

            except Exception as offer_error:
                logging.exception(f"❌ [{i}/{total_offers_processed}] Błąd podczas przetwarzania oferty wynajmu {id}: {offer_error}")
                # Rollback transaction and continue with next offer
                try:
                    conn.rollback()
                    logging.info(f"[{i}/{total_offers_processed}] Transaction rolled back, kontynuuję z następną ofertą")
                except Exception as rollback_error:
                    logging.error(f"[{i}/{total_offers_processed}] Error during rollback: {rollback_error}")
                continue



        # Podsumowanie przetwarzania ofert
        logging.info(f"\n{'='*80}")
        logging.info(f"PODSUMOWANIE PRZETWARZANIA OFERT WYNAJMU:")
        logging.info(f"✅ Nowe oferty dodane do bazy: {new_offers_added}")
        logging.info(f"🔄 Oferty zaktualizowane (zmiana ceny): {updated_offers_count}")
        logging.info(f"📊 Łącznie przetworzonych ofert: {total_offers_processed}")
        logging.info(f"{'='*80}\n")

        # Na końcu sprawdź, czy są jakieś usunięte oferty wynajmu
        logging.info("Rozpoczynam sprawdzanie czy jakieś oferty wynajmu nie zostały usunięte z otodom...")
        deleted_rental_offers = find_closed_rental_offers(all_rental_offers_basic, city, cur)
        closed_offers_count = len(deleted_rental_offers)

        if closed_offers_count > 0:
            logging.info(f"Znaleziono {closed_offers_count} usuniętych ofert - aktualizuję w bazie...")
            for i, deleted_offer in enumerate(deleted_rental_offers, 1):
                logging.info(f"[{i}/{closed_offers_count}] Oznaczam ofertę {deleted_offer[1]} jako nieaktywną...")
                update_deleted_rental_offers(deleted_offer, conn, cur)  # z automatycznym commit
                logging.info(f"✅ [{i}/{closed_offers_count}] Oferta {deleted_offer[1]} oznaczona jako nieaktywna w bazie")
        else:
            logging.info("Nie znaleziono usuniętych ofert.")

        # Wyświetl końcowe podsumowanie
        log_rental_scraping_summary(total_offers_processed, new_offers_added, updated_offers_count, closed_offers_count)

        logging.info(f"\n🎉 Zakończono scraping ofert wynajmu - wszystkie dane zapisane w bazie danych!")
        logging.info(f"📈 Statystyki końcowe:")
        logging.info(f"   • Nowe oferty: {new_offers_added}")
        logging.info(f"   • Zaktualizowane oferty: {updated_offers_count}")
        logging.info(f"   • Zamknięte oferty: {closed_offers_count}")
        logging.info(f"   • Łącznie przetworzonych: {total_offers_processed}")
            
    except Exception as error:
        logging.exception("Error in main rental function:")
    finally: 
        if conn:
            if cur:  # Ensure cur is not None before closing
                cur.close()
            conn.close()


def main_with_custom_city(city_name: str, voivodeship_name: str | None = None, limit_offers: int | None = None):
    """
    Main function with custom city configuration for rental scraping.
    
    Args:
        city_name (str): Name of the city to scrape
        voivodeship_name (str, optional): Name of the voivodeship
        limit_offers (int, optional): Limit number of offers to process (for testing)
    """
    global city, voivodeship, url
    
    city = city_name.lower()
    voivodeship = voivodeship_name.lower() if voivodeship_name else None
    url = get_rental_search_url(city, voivodeship)
    
    logging.info(f"Rozpoczynam scraping ofert wynajmu dla miasta: {city_name}")
    logging.info(f"URL wyszukiwania: {url}")
    
    if limit_offers:
        logging.info(f"Ograniczenie liczby ofert do: {limit_offers}")
    
    main()


def test_rental_scraping():
    """
    Test function for rental scraping with minimal data.
    """
    logging.info("Rozpoczynam test scrapingu ofert wynajmu...")
    
    # Test z ograniczoną liczbą ofert
    main_with_custom_city("warszawa", "mazowieckie", limit_offers=3)
    
    logging.info("Test scrapingu ofert wynajmu zakończony.")


if __name__ == "__main__": 
    # Możesz uruchomić różne tryby:
    
    # 1. Standardowy scraping dla Warszawy
    main()
    
    # 2. Scraping dla innego miasta
    # main_with_custom_city("kraków", "małopolskie")
    
    # 3. Test z ograniczoną liczbą ofert
    # test_rental_scraping()
