#!/usr/bin/env python3
"""
Test script to demonstrate immediate saving of rental data to database.
This script processes a limited number of offers to show real-time database saving.
"""

import os, sys, logging
from colorlog import ColoredFormatter

# Add scraper path
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from datetime import datetime
from scraper.rental_scraper import is_allowed_to_scrape, scrape_rental_offer, get_rental_search_url, log_rental_scraping_summary
from scraper.rental_fetch_and_parse import download_rental_data_from_search_results, check_if_rental_offer_exists, check_if_rental_price_changed
from db.db_setup import create_tables, get_db_connection
from db.rental_db_operations import insert_new_rental_listing, update_active_rental_offers
from config.logging_config import setup_logger

logger = setup_logger()

def test_immediate_save_rental_data(city='warszawa', voivodeship='mazowieckie', max_offers=3):
    """
    Test function that demonstrates immediate saving of rental data.
    
    Args:
        city (str): City name for scraping
        voivodeship (str): Voivodeship name
        max_offers (int): Maximum number of offers to process for testing
    """
    
    logging.info(f"🧪 ROZPOCZYNAM TEST NATYCHMIASTOWEGO ZAPISU DANYCH WYNAJMU")
    logging.info(f"📍 Miasto: {city}, Województwo: {voivodeship}")
    logging.info(f"📊 Maksymalna liczba ofert do przetworzenia: {max_offers}")
    logging.info(f"{'='*80}")
    
    conn = None
    cur = None
    
    # Liczniki
    total_offers_processed = 0
    new_offers_added = 0
    updated_offers_count = 0
    
    try:
        # Połączenie z bazą danych
        logging.info("🔌 Łączę z bazą danych...")
        conn = get_db_connection()
        if conn is None:
            logging.critical("❌ Connection to the database failed")
            return
        cur = conn.cursor()
        logging.info("✅ Połączenie z bazą danych nawiązane")
        
        # Sprawdź czy scraping jest dozwolony
        url = get_rental_search_url(city, voivodeship)
        result = is_allowed_to_scrape(url)
        logging.info(f"🤖 Czy scraping jest dozwolony dla {url}?: {result}")
        
        if not result:
            logging.warning("⚠️ Scraping nie jest dozwolony - kończę test")
            return

        # Utwórz tabele jeżeli nie istnieją
        logging.info("🏗️ Sprawdzam/tworzę tabele w bazie danych...")
        create_tables(cur)
        logging.info("✅ Tabele gotowe")

        # Pobierz podstawowe dane ofert
        logging.info(f"🔍 Pobieranie podstawowych danych z wyniku wyszukiwania...")
        all_rental_offers_basic = download_rental_data_from_search_results(url)
        
        # Ograniczenie dla testów
        all_rental_offers_basic = all_rental_offers_basic[:max_offers]
        total_offers_processed = len(all_rental_offers_basic)
        
        logging.info(f"📋 Znaleziono {len(all_rental_offers_basic)} ofert do przetworzenia")
        logging.info(f"{'='*80}")

        # Przetwarzaj każdą ofertę z natychmiastowym zapisem
        for i, offer in enumerate(all_rental_offers_basic, 1):
            id = offer.get("listing_id")
            if len(str(id)) != 8: 
                logging.debug(f"⏭️ Pomijam ofertę {id} - nieprawidłowy format ID")
                continue
                
            logging.info(f"\n🏠 [{i}/{total_offers_processed}] PRZETWARZAM OFERTĘ {id}")
            logging.info(f"   Powierzchnia: {offer.get('area', 'N/A')} m²")
            logging.info(f"   Cena wynajmu: {offer.get('rental_price', 'N/A')} PLN")
            logging.info(f"   Link: {offer.get('link', 'N/A')}")
            
            try:
                if not check_if_rental_offer_exists(offer, cur):
                    logging.info(f"🆕 [{i}/{total_offers_processed}] Oferta {id} nie istnieje w bazie - pobieranie pełnych danych...")
                    
                    # Pobierz pełne dane oferty
                    offer_data = scrape_rental_offer(offer)
                    
                    if offer_data:
                        logging.info(f"💾 [{i}/{total_offers_processed}] ZAPISUJĘ OFERTĘ {id} DO BAZY DANYCH...")
                        
                        # NATYCHMIASTOWY ZAPIS DO BAZY
                        id_db = insert_new_rental_listing(offer_data, conn, cur)
                        
                        if id_db:
                            new_offers_added += 1
                            logging.info(f"✅ [{i}/{total_offers_processed}] OFERTA {id} ZAPISANA W BAZIE POD ID {id_db}")
                            logging.info(f"   📝 Tytuł: {offer_data.get('title', 'N/A')[:60]}...")
                            logging.info(f"   💰 Cena: {offer_data.get('rental_price', 'N/A')} PLN")
                            logging.info(f"   📍 Lokalizacja: {offer_data.get('city', 'N/A')}, {offer_data.get('street', 'N/A')}")
                            logging.info(f"   🏠 Pokoje: {offer_data.get('rooms_num', 'N/A')}")
                            logging.info(f"   📐 Powierzchnia: {offer_data.get('area', 'N/A')} m²")
                            
                            # Sprawdź czy dane rzeczywiście są w bazie
                            cur.execute("SELECT COUNT(*) FROM apartments_rental_listings WHERE id = %s", (id_db,))
                            count = cur.fetchone()[0]
                            if count > 0:
                                logging.info(f"✅ POTWIERDZENIE: Oferta {id} (ID: {id_db}) znajduje się w bazie danych")
                            else:
                                logging.error(f"❌ BŁĄD: Oferta {id} (ID: {id_db}) NIE znajduje się w bazie danych!")
                        else:
                            logging.warning(f"❌ [{i}/{total_offers_processed}] Nie udało się zapisać oferty {id} w bazie")
                    else:
                        logging.warning(f"❌ [{i}/{total_offers_processed}] Nie udało się pobrać pełnych danych oferty {id}")
                        
                else:
                    # Oferta istnieje - sprawdź zmiany ceny
                    logging.info(f"🔄 [{i}/{total_offers_processed}] Oferta {id} istnieje w bazie - sprawdzanie zmian ceny...")
                    price_check_result = check_if_rental_price_changed(offer, cur)
                    
                    if price_check_result is not None:
                        id_db, new_price, new_price_per_m = price_check_result
                        if new_price is not False and new_price is not None:
                            logging.info(f"💰 [{i}/{total_offers_processed}] AKTUALIZUJĘ CENĘ OFERTY {id} W BAZIE...")
                            
                            # NATYCHMIASTOWA AKTUALIZACJA W BAZIE
                            update_active_rental_offers((id_db, new_price, new_price_per_m), conn, cur)
                            updated_offers_count += 1
                            logging.info(f"✅ [{i}/{total_offers_processed}] CENA OFERTY {id} ZAKTUALIZOWANA")
                            logging.info(f"   💰 Nowa cena: {new_price} PLN")
                            logging.info(f"   📊 Nowa cena za m²: {new_price_per_m} PLN/m²")
                        else:
                            logging.info(f"ℹ️ [{i}/{total_offers_processed}] Cena oferty {id} nie zmieniła się")
                    else:
                        logging.error(f"❌ [{i}/{total_offers_processed}] Błąd sprawdzania ceny dla oferty {id}")
                        
                logging.info(f"✅ [{i}/{total_offers_processed}] Oferta {id} przetworzona pomyślnie")
                
            except Exception as offer_error:
                logging.exception(f"❌ [{i}/{total_offers_processed}] Błąd podczas przetwarzania oferty {id}: {offer_error}")
                try:
                    conn.rollback()
                    logging.info(f"🔄 [{i}/{total_offers_processed}] Transaction rolled back")
                except Exception as rollback_error:
                    logging.error(f"Error during rollback: {rollback_error}")
                continue

        # Podsumowanie testu
        logging.info(f"\n{'='*80}")
        logging.info(f"🎉 TEST NATYCHMIASTOWEGO ZAPISU ZAKOŃCZONY!")
        logging.info(f"📊 STATYSTYKI:")
        logging.info(f"   ✅ Nowe oferty dodane: {new_offers_added}")
        logging.info(f"   🔄 Oferty zaktualizowane: {updated_offers_count}")
        logging.info(f"   📋 Łącznie przetworzonych: {total_offers_processed}")
        logging.info(f"{'='*80}")
        
        # Sprawdź łączną liczbę ofert w bazie
        cur.execute("SELECT COUNT(*) FROM apartments_rental_listings WHERE active = true")
        total_active_offers = cur.fetchone()[0]
        logging.info(f"📈 Łączna liczba aktywnych ofert w bazie: {total_active_offers}")
        
        logging.info(f"✅ Wszystkie dane zostały zapisane natychmiast w bazie danych!")
            
    except Exception as error:
        logging.exception(f"❌ Error in test function: {error}")
    finally: 
        if conn:
            if cur:
                cur.close()
            conn.close()
            logging.info("🔌 Połączenie z bazą danych zamknięte")


if __name__ == "__main__": 
    # Uruchom test z ograniczoną liczbą ofert
    test_immediate_save_rental_data(city='warszawa', voivodeship='mazowieckie', max_offers=2)
